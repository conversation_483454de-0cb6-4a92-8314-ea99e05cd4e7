#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مصمم أنبوب تصريف المياه ثلاثي الأبعاد
تصميم أنبوب PVC بقطر 10 سم وطول 1 متر مع مخرج جانبي بزاوية 45 درجة
"""

import numpy as np
import trimesh
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import os

class PipeDesigner3D:
    def __init__(self):
        """تهيئة مصمم الأنبوب ثلاثي الأبعاد"""
        # المواصفات الهندسية (بالمليمتر)
        self.inner_diameter = 100  # 10 سم
        self.wall_thickness = 5    # 5 مم
        self.outer_diameter = self.inner_diameter + (2 * self.wall_thickness)
        self.main_length = 1000    # 1 متر
        self.branch_angle = 45     # زاوية المخرج الجانبي
        self.branch_length = 200   # طول المخرج الجانبي
        
        # دقة التصميم
        self.resolution = 32       # عدد النقاط في المحيط
        
        print(f"🔧 مواصفات الأنبوب:")
        print(f"   القطر الداخلي: {self.inner_diameter} مم")
        print(f"   سماكة الجدار: {self.wall_thickness} مم")
        print(f"   القطر الخارجي: {self.outer_diameter} مم")
        print(f"   الطول الرئيسي: {self.main_length} مم")
        print(f"   زاوية المخرج: {self.branch_angle}°")
    
    def create_cylinder(self, radius, height, center=(0, 0, 0)):
        """إنشاء أسطوانة"""
        return trimesh.creation.cylinder(
            radius=radius,
            height=height,
            sections=self.resolution
        ).apply_translation(center)
    
    def create_main_pipe(self):
        """إنشاء الأنبوب الرئيسي"""
        print("🔨 إنشاء الأنبوب الرئيسي...")
        
        # الأسطوانة الخارجية
        outer_cylinder = self.create_cylinder(
            radius=self.outer_diameter/2,
            height=self.main_length,
            center=(0, 0, self.main_length/2)
        )
        
        # الأسطوانة الداخلية (للحفر)
        inner_cylinder = self.create_cylinder(
            radius=self.inner_diameter/2,
            height=self.main_length + 10,  # أطول قليلاً للتأكد من الحفر الكامل
            center=(0, 0, self.main_length/2)
        )
        
        # طرح الأسطوانة الداخلية من الخارجية
        main_pipe = outer_cylinder.difference(inner_cylinder)
        
        return main_pipe
    
    def create_branch_pipe(self):
        """إنشاء المخرج الجانبي"""
        print("🔨 إنشاء المخرج الجانبي...")
        
        # حساب موقع المخرج الجانبي (في منتصف الأنبوب الرئيسي)
        branch_z = self.main_length / 2
        
        # الأسطوانة الخارجية للمخرج
        outer_branch = self.create_cylinder(
            radius=self.outer_diameter/2,
            height=self.branch_length,
            center=(0, 0, 0)
        )
        
        # الأسطوانة الداخلية للمخرج
        inner_branch = self.create_cylinder(
            radius=self.inner_diameter/2,
            height=self.branch_length + 10,
            center=(0, 0, 0)
        )
        
        # طرح الأسطوانة الداخلية من الخارجية
        branch_pipe = outer_branch.difference(inner_branch)
        
        # تدوير المخرج بزاوية 45 درجة
        rotation_matrix = trimesh.transformations.rotation_matrix(
            np.radians(self.branch_angle), [0, 1, 0]
        )
        branch_pipe.apply_transform(rotation_matrix)
        
        # تحريك المخرج إلى الموقع الصحيح
        branch_offset_x = (self.outer_diameter/2) * np.cos(np.radians(self.branch_angle))
        branch_offset_z = (self.outer_diameter/2) * np.sin(np.radians(self.branch_angle))
        
        translation = [
            self.outer_diameter/2 + branch_offset_x,
            0,
            branch_z + branch_offset_z
        ]
        branch_pipe.apply_translation(translation)
        
        return branch_pipe
    
    def create_junction(self):
        """إنشاء نقطة التقاء الأنبوب الرئيسي مع المخرج الجانبي"""
        print("🔨 إنشاء نقطة الالتقاء...")
        
        # إنشاء كرة في نقطة الالتقاء لضمان الاتصال السلس
        junction_center = [
            self.outer_diameter/2,
            0,
            self.main_length/2
        ]
        
        junction_sphere = trimesh.creation.icosphere(
            subdivisions=2,
            radius=self.outer_diameter/2 * 1.2
        ).apply_translation(junction_center)
        
        # حفر الداخل
        inner_sphere = trimesh.creation.icosphere(
            subdivisions=2,
            radius=self.inner_diameter/2 * 1.2
        ).apply_translation(junction_center)
        
        junction = junction_sphere.difference(inner_sphere)
        
        return junction
    
    def create_complete_pipe(self):
        """إنشاء الأنبوب الكامل"""
        print("🏗️ تجميع الأنبوب الكامل...")
        
        # إنشاء المكونات
        main_pipe = self.create_main_pipe()
        branch_pipe = self.create_branch_pipe()
        junction = self.create_junction()
        
        # دمج جميع المكونات
        complete_pipe = main_pipe.union(branch_pipe)
        complete_pipe = complete_pipe.union(junction)
        
        # تنظيف الشبكة وإصلاح أي مشاكل
        complete_pipe.remove_duplicate_faces()
        complete_pipe.remove_degenerate_faces()
        complete_pipe.fill_holes()
        
        # التأكد من أن الشبكة مغلقة ومناسبة للطباعة
        if not complete_pipe.is_watertight:
            print("⚠️ تحذير: الشبكة غير مغلقة تماماً، محاولة الإصلاح...")
            complete_pipe.fix_normals()
        
        print(f"✅ تم إنشاء الأنبوب بنجاح!")
        print(f"   عدد الوجوه: {len(complete_pipe.faces)}")
        print(f"   عدد الرؤوس: {len(complete_pipe.vertices)}")
        print(f"   الحجم: {complete_pipe.volume/1000:.2f} سم³")
        print(f"   مغلق: {'نعم' if complete_pipe.is_watertight else 'لا'}")
        
        return complete_pipe
    
    def save_files(self, pipe_mesh, base_name="drainage_pipe"):
        """حفظ الملفات بصيغ مختلفة"""
        print("💾 حفظ الملفات...")
        
        # إنشاء مجلد للملفات
        output_dir = "3d_pipe_output"
        os.makedirs(output_dir, exist_ok=True)
        
        # حفظ ملف STL للطباعة ثلاثية الأبعاد
        stl_path = os.path.join(output_dir, f"{base_name}.stl")
        pipe_mesh.export(stl_path)
        print(f"   ✅ STL: {stl_path}")
        
        # حفظ ملف OBJ للعرض والتحرير
        obj_path = os.path.join(output_dir, f"{base_name}.obj")
        pipe_mesh.export(obj_path)
        print(f"   ✅ OBJ: {obj_path}")
        
        # حفظ ملف PLY
        ply_path = os.path.join(output_dir, f"{base_name}.ply")
        pipe_mesh.export(ply_path)
        print(f"   ✅ PLY: {ply_path}")
        
        return output_dir
    
    def create_visualizations(self, pipe_mesh, output_dir):
        """إنشاء صور مرئية للتصميم"""
        print("📸 إنشاء الصور المرئية...")
        
        # إعداد الرسم
        fig = plt.figure(figsize=(20, 15))
        
        # منظر ثلاثي الأبعاد رئيسي
        ax1 = fig.add_subplot(2, 3, 1, projection='3d')
        ax1.plot_trisurf(pipe_mesh.vertices[:, 0], 
                        pipe_mesh.vertices[:, 1], 
                        pipe_mesh.vertices[:, 2], 
                        triangles=pipe_mesh.faces,
                        alpha=0.8, color='lightblue', edgecolor='navy', linewidth=0.1)
        ax1.set_title('منظر ثلاثي الأبعاد شامل', fontsize=14, pad=20)
        ax1.set_xlabel('X (مم)')
        ax1.set_ylabel('Y (مم)')
        ax1.set_zlabel('Z (مم)')
        
        # منظر من الأمام
        ax2 = fig.add_subplot(2, 3, 2, projection='3d')
        ax2.plot_trisurf(pipe_mesh.vertices[:, 0], 
                        pipe_mesh.vertices[:, 1], 
                        pipe_mesh.vertices[:, 2], 
                        triangles=pipe_mesh.faces,
                        alpha=0.8, color='lightgreen', edgecolor='darkgreen', linewidth=0.1)
        ax2.view_init(elev=0, azim=0)
        ax2.set_title('منظر أمامي', fontsize=14, pad=20)
        ax2.set_xlabel('X (مم)')
        ax2.set_ylabel('Y (مم)')
        ax2.set_zlabel('Z (مم)')
        
        # منظر من الجانب
        ax3 = fig.add_subplot(2, 3, 3, projection='3d')
        ax3.plot_trisurf(pipe_mesh.vertices[:, 0], 
                        pipe_mesh.vertices[:, 1], 
                        pipe_mesh.vertices[:, 2], 
                        triangles=pipe_mesh.faces,
                        alpha=0.8, color='lightcoral', edgecolor='darkred', linewidth=0.1)
        ax3.view_init(elev=0, azim=90)
        ax3.set_title('منظر جانبي', fontsize=14, pad=20)
        ax3.set_xlabel('X (مم)')
        ax3.set_ylabel('Y (مم)')
        ax3.set_zlabel('Z (مم)')
        
        # منظر من الأعلى
        ax4 = fig.add_subplot(2, 3, 4, projection='3d')
        ax4.plot_trisurf(pipe_mesh.vertices[:, 0], 
                        pipe_mesh.vertices[:, 1], 
                        pipe_mesh.vertices[:, 2], 
                        triangles=pipe_mesh.faces,
                        alpha=0.8, color='gold', edgecolor='orange', linewidth=0.1)
        ax4.view_init(elev=90, azim=0)
        ax4.set_title('منظر علوي', fontsize=14, pad=20)
        ax4.set_xlabel('X (مم)')
        ax4.set_ylabel('Y (مم)')
        ax4.set_zlabel('Z (مم)')
        
        # منظر مقطعي
        ax5 = fig.add_subplot(2, 3, 5, projection='3d')
        # إنشاء مقطع في منتصف الأنبوب
        section_plane = trimesh.path.Path3D(entities=[
            trimesh.path.entities.Line([0, 1])
        ], vertices=np.array([
            [0, -self.outer_diameter, self.main_length/2],
            [0, self.outer_diameter, self.main_length/2]
        ]))
        
        ax5.plot_trisurf(pipe_mesh.vertices[:, 0], 
                        pipe_mesh.vertices[:, 1], 
                        pipe_mesh.vertices[:, 2], 
                        triangles=pipe_mesh.faces,
                        alpha=0.6, color='purple', edgecolor='indigo', linewidth=0.1)
        ax5.set_title('منظر مقطعي', fontsize=14, pad=20)
        ax5.set_xlabel('X (مم)')
        ax5.set_ylabel('Y (مم)')
        ax5.set_zlabel('Z (مم)')
        
        # منظر تفصيلي للمخرج الجانبي
        ax6 = fig.add_subplot(2, 3, 6, projection='3d')
        ax6.plot_trisurf(pipe_mesh.vertices[:, 0], 
                        pipe_mesh.vertices[:, 1], 
                        pipe_mesh.vertices[:, 2], 
                        triangles=pipe_mesh.faces,
                        alpha=0.8, color='cyan', edgecolor='teal', linewidth=0.1)
        ax6.view_init(elev=30, azim=45)
        ax6.set_title('منظر تفصيلي للمخرج الجانبي', fontsize=14, pad=20)
        ax6.set_xlabel('X (مم)')
        ax6.set_ylabel('Y (مم)')
        ax6.set_zlabel('Z (مم)')
        
        plt.tight_layout()
        
        # حفظ الصورة
        image_path = os.path.join(output_dir, "pipe_visualizations.png")
        plt.savefig(image_path, dpi=300, bbox_inches='tight')
        print(f"   ✅ صورة مرئية: {image_path}")
        
        plt.close()
        
        return image_path

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء تصميم أنبوب تصريف المياه ثلاثي الأبعاد")
    print("=" * 60)
    
    # إنشاء مصمم الأنبوب
    designer = PipeDesigner3D()
    
    # إنشاء التصميم الكامل
    pipe_mesh = designer.create_complete_pipe()
    
    # حفظ الملفات
    output_dir = designer.save_files(pipe_mesh)
    
    # إنشاء الصور المرئية
    image_path = designer.create_visualizations(pipe_mesh, output_dir)
    
    print("\n" + "=" * 60)
    print("✅ تم إنشاء التصميم ثلاثي الأبعاد بنجاح!")
    print(f"📁 الملفات محفوظة في: {output_dir}")
    print("\n📋 الملفات المُنشأة:")
    print("   • drainage_pipe.stl - للطباعة ثلاثية الأبعاد")
    print("   • drainage_pipe.obj - للعرض والتحرير")
    print("   • drainage_pipe.ply - تنسيق إضافي")
    print("   • pipe_visualizations.png - صور مرئية")
    
    print("\n🔧 مواصفات التصميم:")
    print(f"   • القطر الداخلي: 10 سم")
    print(f"   • سماكة الجدار: 5 مم")
    print(f"   • الطول الرئيسي: 1 متر")
    print(f"   • زاوية المخرج الجانبي: 45°")
    print(f"   • المادة: PVC")
    print(f"   • قابل للطباعة ثلاثية الأبعاد: نعم")

if __name__ == "__main__":
    main()
