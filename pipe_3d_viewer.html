<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>عارض أنبوب التصريف ثلاثي الأبعاد - 3D Drainage Pipe Viewer</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: '<PERSON><PERSON>', 'Arial', sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            overflow-x: hidden;
        }

        .header {
            text-align: center;
            padding: 20px;
            background: rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(10px);
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .specs-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .spec-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: transform 0.3s ease;
        }

        .spec-card:hover {
            transform: translateY(-5px);
        }

        .spec-card h3 {
            color: #ffd700;
            margin-bottom: 15px;
            font-size: 1.4rem;
        }

        .spec-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 8px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .spec-label {
            font-weight: bold;
        }

        .spec-value {
            color: #87ceeb;
        }

        .viewer-section {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            text-align: center;
        }

        .viewer-placeholder {
            width: 100%;
            height: 400px;
            background: linear-gradient(45deg, #333, #555);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 20px 0;
            border: 2px dashed #ffd700;
            position: relative;
            overflow: hidden;
        }

        .pipe-diagram {
            width: 80%;
            height: 80%;
            position: relative;
        }

        .main-pipe {
            width: 60%;
            height: 80%;
            background: linear-gradient(to bottom, #4a90e2, #357abd);
            border-radius: 10px;
            position: absolute;
            left: 20%;
            top: 10%;
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
            border: 3px solid #2c5aa0;
        }

        .branch-pipe {
            width: 30%;
            height: 15%;
            background: linear-gradient(45deg, #4a90e2, #357abd);
            border-radius: 8px;
            position: absolute;
            right: 10%;
            top: 40%;
            transform: rotate(45deg);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
            border: 2px solid #2c5aa0;
        }

        .dimension-label {
            position: absolute;
            background: rgba(255, 215, 0, 0.9);
            color: #000;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8rem;
            font-weight: bold;
        }

        .label-length {
            bottom: -25px;
            left: 50%;
            transform: translateX(-50%);
        }

        .label-diameter {
            right: -60px;
            top: 50%;
            transform: translateY(-50%);
        }

        .label-branch {
            right: -40px;
            top: 20%;
        }

        .download-section {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .download-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }

        .download-card:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: scale(1.05);
        }

        .download-btn {
            background: linear-gradient(45deg, #ffd700, #ffed4e);
            color: #000;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            font-size: 1rem;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 10px;
            width: 100%;
        }

        .download-btn:hover {
            background: linear-gradient(45deg, #ffed4e, #ffd700);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(255, 215, 0, 0.4);
        }

        .instructions {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
        }

        .instructions h3 {
            color: #ffd700;
            margin-bottom: 20px;
            font-size: 1.6rem;
        }

        .instruction-step {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 15px;
            border-right: 4px solid #ffd700;
        }

        .step-number {
            background: #ffd700;
            color: #000;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-left: 10px;
        }

        .footer {
            text-align: center;
            padding: 20px;
            background: rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(10px);
            margin-top: 40px;
        }

        @media (max-width: 768px) {
            .header h1 {
                font-size: 2rem;
            }
            
            .specs-grid {
                grid-template-columns: 1fr;
            }
            
            .container {
                padding: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>عارض أنبوب التصريف ثلاثي الأبعاد</h1>
        <p>تصميم أنبوب تصريف مياه بقطر 10 سم وطول 1 متر مع مخرج جانبي بزاوية 45 درجة</p>
    </div>

    <div class="container">
        <!-- مواصفات التصميم -->
        <div class="specs-grid">
            <div class="spec-card">
                <h3>🔧 المواصفات الأساسية</h3>
                <div class="spec-item">
                    <span class="spec-label">القطر الرئيسي:</span>
                    <span class="spec-value">10 سم</span>
                </div>
                <div class="spec-item">
                    <span class="spec-label">الطول الكلي:</span>
                    <span class="spec-value">1 متر</span>
                </div>
                <div class="spec-item">
                    <span class="spec-label">سماكة الجدار:</span>
                    <span class="spec-value">5 مم</span>
                </div>
                <div class="spec-item">
                    <span class="spec-label">المادة:</span>
                    <span class="spec-value">PVC</span>
                </div>
            </div>

            <div class="spec-card">
                <h3>📐 المخرج الجانبي</h3>
                <div class="spec-item">
                    <span class="spec-label">قطر المخرج:</span>
                    <span class="spec-value">8 سم</span>
                </div>
                <div class="spec-item">
                    <span class="spec-label">طول المخرج:</span>
                    <span class="spec-value">15 سم</span>
                </div>
                <div class="spec-item">
                    <span class="spec-label">زاوية الميل:</span>
                    <span class="spec-value">45 درجة</span>
                </div>
                <div class="spec-item">
                    <span class="spec-label">الموضع:</span>
                    <span class="spec-value">70 سم من البداية</span>
                </div>
            </div>

            <div class="spec-card">
                <h3>🏭 مواصفات الطباعة</h3>
                <div class="spec-item">
                    <span class="spec-label">سماكة الطبقة:</span>
                    <span class="spec-value">0.2-0.3 مم</span>
                </div>
                <div class="spec-item">
                    <span class="spec-label">الملء:</span>
                    <span class="spec-value">20-30%</span>
                </div>
                <div class="spec-item">
                    <span class="spec-label">الدعامات:</span>
                    <span class="spec-value">مطلوبة</span>
                </div>
                <div class="spec-item">
                    <span class="spec-label">وقت الطباعة:</span>
                    <span class="spec-value">8-12 ساعة</span>
                </div>
            </div>
        </div>

        <!-- عارض التصميم -->
        <div class="viewer-section">
            <h3>📊 مخطط التصميم</h3>
            <div class="viewer-placeholder">
                <div class="pipe-diagram">
                    <div class="main-pipe">
                        <div class="dimension-label label-length">1 متر</div>
                        <div class="dimension-label label-diameter">⌀ 10 سم</div>
                    </div>
                    <div class="branch-pipe">
                        <div class="dimension-label label-branch">⌀ 8 سم<br>45°</div>
                    </div>
                </div>
            </div>
            <p>مخطط توضيحي للتصميم - للعرض ثلاثي الأبعاد الكامل، استخدم OpenSCAD</p>
        </div>

        <!-- تحميل الملفات -->
        <div class="download-section">
            <div class="download-card">
                <h4>📁 ملف OpenSCAD</h4>
                <p>ملف التصميم الأساسي القابل للتعديل</p>
                <button class="download-btn" onclick="downloadFile('أنبوب_تصريف_أساسي_10_سم.scad')">
                    تحميل .scad
                </button>
            </div>

            <div class="download-card">
                <h4>🎯 ملف STL</h4>
                <p>جاهز للطباعة ثلاثية الأبعاد</p>
                <button class="download-btn" onclick="downloadFile('drainage_pipe_10cm_1m.stl')">
                    تحميل .stl
                </button>
            </div>

            <div class="download-card">
                <h4>📋 كتالوج التصميمات</h4>
                <p>جميع المتغيرات المتاحة</p>
                <button class="download-btn" onclick="downloadFile('design_catalog.json')">
                    تحميل الكتالوج
                </button>
            </div>

            <div class="download-card">
                <h4>📖 دليل الاستخدام</h4>
                <p>تعليمات مفصلة للطباعة والتركيب</p>
                <button class="download-btn" onclick="downloadFile('README.md')">
                    تحميل الدليل
                </button>
            </div>
        </div>

        <!-- تعليمات الاستخدام -->
        <div class="instructions">
            <h3>📋 تعليمات الاستخدام</h3>
            
            <div class="instruction-step">
                <span class="step-number">1</span>
                <strong>تثبيت OpenSCAD:</strong> قم بتحميل وتثبيت OpenSCAD من الموقع الرسمي
            </div>

            <div class="instruction-step">
                <span class="step-number">2</span>
                <strong>فتح التصميم:</strong> افتح ملف .scad في OpenSCAD لعرض النموذج ثلاثي الأبعاد
            </div>

            <div class="instruction-step">
                <span class="step-number">3</span>
                <strong>التخصيص:</strong> عدّل المعاملات في بداية الملف حسب احتياجاتك
            </div>

            <div class="instruction-step">
                <span class="step-number">4</span>
                <strong>التصدير:</strong> صدّر التصميم إلى ملف STL للطباعة ثلاثية الأبعاد
            </div>

            <div class="instruction-step">
                <span class="step-number">5</span>
                <strong>الطباعة:</strong> استخدم إعدادات الطباعة المقترحة مع الدعامات المناسبة
            </div>
        </div>
    </div>

    <div class="footer">
        <p>تصميم أنبوب تصريف المياه ثلاثي الأبعاد - جاهز للطباعة والاستخدام</p>
        <p>تم إنشاؤه باستخدام OpenSCAD و Python</p>
    </div>

    <script>
        function downloadFile(filename) {
            // محاولة تحميل الملف إذا كان متوفراً
            const link = document.createElement('a');
            link.href = filename;
            link.download = filename;
            
            // التحقق من وجود الملف
            fetch(filename)
                .then(response => {
                    if (response.ok) {
                        link.click();
                    } else {
                        alert(`الملف ${filename} غير متوفر حالياً. تأكد من وجود الملف في نفس المجلد.`);
                    }
                })
                .catch(error => {
                    alert(`الملف ${filename} غير متوفر حالياً. تأكد من وجود الملف في نفس المجلد.`);
                });
        }

        // تأثيرات بصرية
        document.addEventListener('DOMContentLoaded', function() {
            // تحريك العناصر عند التحميل
            const cards = document.querySelectorAll('.spec-card, .download-card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                
                setTimeout(() => {
                    card.style.transition = 'all 0.6s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 100);
            });

            // تحريك المخطط
            const diagram = document.querySelector('.pipe-diagram');
            if (diagram) {
                diagram.style.animation = 'float 3s ease-in-out infinite';
            }
        });

        // إضافة CSS للتحريك
        const style = document.createElement('style');
        style.textContent = `
            @keyframes float {
                0%, 100% { transform: translateY(0px); }
                50% { transform: translateY(-10px); }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
