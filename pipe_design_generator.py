#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مولد تصميمات أنابيب التصريف المتعددة
Multiple Drainage Pipe Design Generator
"""

import json
import os
from datetime import datetime

class PipeDesignGenerator:
    def __init__(self):
        self.designs = {}
        self.current_design = None
        
    def create_design(self, name, specs):
        """إنشاء تصميم جديد"""
        design = {
            'name': name,
            'created': datetime.now().isoformat(),
            'specs': specs,
            'files': []
        }
        self.designs[name] = design
        self.current_design = name
        return design
    
    def generate_parametric_scad(self, name, specs):
        """إنشاء ملف OpenSCAD بارامتري"""
        
        scad_template = f"""
// {name} - تصميم أنبوب تصريف مياه بارامتري
// Parametric Drainage Pipe Design

// === المعاملات القابلة للتخصيص ===
// Customizable Parameters

// الأبعاد الأساسية - Basic Dimensions
main_diameter = {specs['main_diameter']};           // القطر الرئيسي (مم)
main_length = {specs['main_length']};               // الطول الكلي (مم)
wall_thickness = {specs['wall_thickness']};         // سماكة الجدار (مم)

// المخرج الجانبي - Side Branch
branch_diameter = {specs['branch_diameter']};       // قطر المخرج الجانبي (مم)
branch_length = {specs['branch_length']};           // طول المخرج الجانبي (مم)
branch_angle = {specs['branch_angle']};             // زاوية المخرج (درجة)
branch_position = {specs['branch_position']};       // موضع المخرج من البداية (مم)

// خيارات التصميم - Design Options
include_end_caps = {str(specs.get('end_caps', True)).lower()};      // أغطية النهايات
include_drainage_holes = {str(specs.get('drainage_holes', True)).lower()};  // فتحات التصريف
include_support_ribs = {str(specs.get('support_ribs', True)).lower()};      // أضلاع الدعم
include_reinforcement = {str(specs.get('reinforcement', True)).lower()};    // التقوية

// معاملات الطباعة - Printing Parameters
print_tolerance = {specs.get('print_tolerance', 0.2)};    // تسامح الطباعة (مم)
support_angle = {specs.get('support_angle', 45)};         // زاوية الدعامات

// دقة النموذج - Model Resolution
$fn = {specs.get('resolution', 64)};

// === الوحدات الأساسية ===
// Basic Modules

module main_pipe() {{
    difference() {{
        cylinder(h = main_length, d = main_diameter);
        translate([0, 0, -1])
        cylinder(h = main_length + 2, d = main_diameter - 2*wall_thickness);
    }}
}}

module branch_pipe() {{
    translate([0, 0, branch_position]) {{
        rotate([0, branch_angle, 0]) {{
            translate([main_diameter/2 - wall_thickness, 0, 0]) {{
                difference() {{
                    cylinder(h = branch_length, d = branch_diameter);
                    translate([0, 0, -1])
                    cylinder(h = branch_length + 2, d = branch_diameter - 2*wall_thickness);
                }}
            }}
        }}
    }}
}}

module connection_joint() {{
    if (include_reinforcement) {{
        translate([0, 0, branch_position]) {{
            rotate([0, branch_angle, 0]) {{
                translate([main_diameter/2 - wall_thickness - 5, 0, -10]) {{
                    difference() {{
                        cylinder(h = 20, d = branch_diameter + 10);
                        translate([0, 0, -1])
                        cylinder(h = 22, d = branch_diameter - 2*wall_thickness);
                    }}
                }}
            }}
        }}
    }}
}}

module drainage_hole() {{
    translate([0, 0, branch_position]) {{
        rotate([0, branch_angle, 0]) {{
            translate([main_diameter/2 - wall_thickness - 1, 0, 0]) {{
                cylinder(h = 20, d = branch_diameter - 2*wall_thickness, center = true);
            }}
        }}
    }}
}}

module end_caps() {{
    if (include_end_caps) {{
        // الغطاء السفلي
        difference() {{
            cylinder(h = wall_thickness, d = main_diameter);
            translate([0, 0, -1])
            cylinder(h = wall_thickness + 2, d = main_diameter - 4*wall_thickness);
        }}
        
        // الغطاء العلوي
        translate([0, 0, main_length - wall_thickness]) {{
            difference() {{
                cylinder(h = wall_thickness, d = main_diameter);
                translate([0, 0, -1])
                cylinder(h = wall_thickness + 2, d = main_diameter - 4*wall_thickness);
            }}
        }}
    }}
}}

module drainage_slots() {{
    if (include_drainage_holes) {{
        for (i = [0:45:315]) {{
            rotate([0, 0, i]) {{
                translate([main_diameter/2 - wall_thickness/2, 0, main_length * 0.25]) {{
                    cube([wall_thickness + 1, 8, 20], center = true);
                }}
            }}
        }}
        
        for (i = [0:45:315]) {{
            rotate([0, 0, i]) {{
                translate([main_diameter/2 - wall_thickness/2, 0, main_length * 0.75]) {{
                    cube([wall_thickness + 1, 8, 20], center = true);
                }}
            }}
        }}
    }}
}}

module support_ribs() {{
    if (include_support_ribs) {{
        for (i = [0:90:270]) {{
            rotate([0, 0, i]) {{
                translate([0, 0, main_length/2]) {{
                    cube([main_diameter - 2*wall_thickness - 10, 2, main_length - 100], center = true);
                }}
            }}
        }}
    }}
}}

// === التجميع النهائي ===
// Final Assembly

module complete_pipe() {{
    difference() {{
        union() {{
            main_pipe();
            branch_pipe();
            connection_joint();
            end_caps();
        }}
        
        drainage_hole();
        drainage_slots();
    }}
    
    support_ribs();
}}

// عرض النموذج
complete_pipe();

// === معلومات التصميم ===
// Design Information

echo("=== {name} ===");
echo(str("القطر الرئيسي: ", main_diameter, " مم"));
echo(str("الطول: ", main_length, " مم"));
echo(str("سماكة الجدار: ", wall_thickness, " مم"));
echo(str("قطر الفرع: ", branch_diameter, " مم"));
echo(str("زاوية الفرع: ", branch_angle, " درجة"));

// حساب الخصائص
main_volume = PI * pow(main_diameter/2, 2) * main_length / 1000000; // لتر
material_volume = PI * (pow(main_diameter/2, 2) - pow((main_diameter-2*wall_thickness)/2, 2)) * main_length / 1000000; // لتر
weight_pvc = material_volume * 1.4; // كيلوجرام (كثافة PVC)

echo(str("السعة الداخلية: ", main_volume, " لتر"));
echo(str("حجم المادة: ", material_volume, " لتر"));
echo(str("الوزن التقريبي (PVC): ", weight_pvc, " كجم"));
"""
        
        filename = f"{name.replace(' ', '_')}.scad"
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(scad_template)
        
        return filename
    
    def generate_design_variants(self):
        """إنشاء متغيرات مختلفة من التصميم"""
        
        # التصميم الأساسي - 10 سم
        basic_specs = {
            'main_diameter': 100,
            'main_length': 1000,
            'wall_thickness': 5,
            'branch_diameter': 80,
            'branch_length': 150,
            'branch_angle': 45,
            'branch_position': 700,
            'end_caps': True,
            'drainage_holes': True,
            'support_ribs': True,
            'reinforcement': True,
            'print_tolerance': 0.2,
            'resolution': 64
        }
        
        # تصميم صغير - 6 سم
        small_specs = basic_specs.copy()
        small_specs.update({
            'main_diameter': 60,
            'main_length': 600,
            'branch_diameter': 50,
            'branch_length': 100,
            'branch_position': 400
        })
        
        # تصميم كبير - 15 سم
        large_specs = basic_specs.copy()
        large_specs.update({
            'main_diameter': 150,
            'main_length': 1500,
            'wall_thickness': 8,
            'branch_diameter': 120,
            'branch_length': 200,
            'branch_position': 1000
        })
        
        # تصميم بدون فرع جانبي
        straight_specs = basic_specs.copy()
        straight_specs.update({
            'branch_diameter': 0,
            'branch_length': 0,
            'branch_angle': 0
        })
        
        designs = [
            ("أنبوب تصريف أساسي 10 سم", basic_specs),
            ("أنبوب تصريف صغير 6 سم", small_specs),
            ("أنبوب تصريف كبير 15 سم", large_specs),
            ("أنبوب تصريف مستقيم", straight_specs)
        ]
        
        generated_files = []
        
        for name, specs in designs:
            design = self.create_design(name, specs)
            filename = self.generate_parametric_scad(name, specs)
            design['files'].append(filename)
            generated_files.append(filename)
            print(f"تم إنشاء: {filename}")
        
        return generated_files
    
    def save_design_catalog(self, filename="design_catalog.json"):
        """حفظ كتالوج التصميمات"""
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(self.designs, f, ensure_ascii=False, indent=2)
        
        print(f"تم حفظ كتالوج التصميمات: {filename}")
    
    def generate_readme(self):
        """إنشاء ملف README مع التعليمات"""
        readme_content = """
# مولد تصميمات أنابيب التصريف
# Drainage Pipe Design Generator

## الملفات المُنشأة:

### التصميمات المتاحة:
1. **أنبوب تصريف أساسي 10 سم** - التصميم المطلوب الأساسي
2. **أنبوب تصريف صغير 6 سم** - للاستخدامات الخفيفة
3. **أنبوب تصريف كبير 15 سم** - للتطبيقات الثقيلة
4. **أنبوب تصريف مستقيم** - بدون مخرج جانبي

### كيفية الاستخدام:

#### 1. عرض التصميمات:
- قم بتثبيت OpenSCAD من: https://openscad.org/
- افتح أي ملف .scad في OpenSCAD
- اضغط F5 للمعاينة السريعة
- اضغط F6 للرندر النهائي عالي الجودة

#### 2. تخصيص التصميم:
- افتح الملف .scad في محرر نصوص
- عدّل القيم في قسم "المعاملات القابلة للتخصيص"
- احفظ الملف وأعد تحميله في OpenSCAD

#### 3. تصدير للطباعة ثلاثية الأبعاد:
- في OpenSCAD، اذهب إلى File > Export > Export as STL
- احفظ الملف بصيغة .stl
- استخدم برنامج التقطيع المفضل لديك

### إعدادات الطباعة المقترحة:
- **سماكة الطبقة**: 0.2-0.3 مم
- **الملء**: 20-30%
- **الدعامات**: مطلوبة للمخارج الجانبية
- **سرعة الطباعة**: 50-60 مم/ثانية

### ملاحظات مهمة:
- تأكد من معايرة الطابعة قبل البدء
- استخدم مادة PLA للنماذج الأولية
- استخدم ABS أو PETG للاستخدام الفعلي
- قم بإجراء اختبار تسرب بعد الطباعة

### الدعم الفني:
لأي استفسارات أو مشاكل، يرجى مراجعة:
- دليل OpenSCAD: https://openscad.org/documentation.html
- مجتمع الطباعة ثلاثية الأبعاد المحلي
"""
        
        with open("README.md", 'w', encoding='utf-8') as f:
            f.write(readme_content)
        
        print("تم إنشاء ملف README.md")

def main():
    """الدالة الرئيسية"""
    print("مولد تصميمات أنابيب التصريف المتعددة")
    print("=" * 50)
    
    generator = PipeDesignGenerator()
    
    # إنشاء التصميمات المختلفة
    files = generator.generate_design_variants()
    
    # حفظ كتالوج التصميمات
    generator.save_design_catalog()
    
    # إنشاء ملف README
    generator.generate_readme()
    
    print("\n" + "=" * 50)
    print("تم إنشاء جميع التصميمات بنجاح!")
    print(f"عدد التصميمات: {len(files)}")
    print("\nالملفات المُنشأة:")
    for file in files:
        print(f"  - {file}")
    print("  - design_catalog.json")
    print("  - README.md")
    
    print("\nالخطوات التالية:")
    print("1. قم بتثبيت OpenSCAD")
    print("2. افتح أي ملف .scad لعرض التصميم")
    print("3. صدّر إلى STL للطباعة ثلاثية الأبعاد")

if __name__ == "__main__":
    main()
