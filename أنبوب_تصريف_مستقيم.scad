
// أنبوب تصريف مستقيم - تصميم أنبوب تصريف مياه بارامتري
// Parametric Drainage Pipe Design

// === المعاملات القابلة للتخصيص ===
// Customizable Parameters

// الأبعاد الأساسية - Basic Dimensions
main_diameter = 100;           // القطر الرئيسي (مم)
main_length = 1000;               // الطول الكلي (مم)
wall_thickness = 5;         // سماكة الجدار (مم)

// المخرج الجانبي - Side Branch
branch_diameter = 0;       // قطر المخرج الجانبي (مم)
branch_length = 0;           // طول المخرج الجانبي (مم)
branch_angle = 0;             // زاوية المخرج (درجة)
branch_position = 700;       // موضع المخرج من البداية (مم)

// خيارات التصميم - Design Options
include_end_caps = true;      // أغطية النهايات
include_drainage_holes = true;  // فتحات التصريف
include_support_ribs = true;      // أضلاع الدعم
include_reinforcement = true;    // التقوية

// معاملات الطباعة - Printing Parameters
print_tolerance = 0.2;    // تسامح الطباعة (مم)
support_angle = 45;         // زاوية الدعامات

// دقة النموذج - Model Resolution
$fn = 64;

// === الوحدات الأساسية ===
// Basic Modules

module main_pipe() {
    difference() {
        cylinder(h = main_length, d = main_diameter);
        translate([0, 0, -1])
        cylinder(h = main_length + 2, d = main_diameter - 2*wall_thickness);
    }
}

module branch_pipe() {
    translate([0, 0, branch_position]) {
        rotate([0, branch_angle, 0]) {
            translate([main_diameter/2 - wall_thickness, 0, 0]) {
                difference() {
                    cylinder(h = branch_length, d = branch_diameter);
                    translate([0, 0, -1])
                    cylinder(h = branch_length + 2, d = branch_diameter - 2*wall_thickness);
                }
            }
        }
    }
}

module connection_joint() {
    if (include_reinforcement) {
        translate([0, 0, branch_position]) {
            rotate([0, branch_angle, 0]) {
                translate([main_diameter/2 - wall_thickness - 5, 0, -10]) {
                    difference() {
                        cylinder(h = 20, d = branch_diameter + 10);
                        translate([0, 0, -1])
                        cylinder(h = 22, d = branch_diameter - 2*wall_thickness);
                    }
                }
            }
        }
    }
}

module drainage_hole() {
    translate([0, 0, branch_position]) {
        rotate([0, branch_angle, 0]) {
            translate([main_diameter/2 - wall_thickness - 1, 0, 0]) {
                cylinder(h = 20, d = branch_diameter - 2*wall_thickness, center = true);
            }
        }
    }
}

module end_caps() {
    if (include_end_caps) {
        // الغطاء السفلي
        difference() {
            cylinder(h = wall_thickness, d = main_diameter);
            translate([0, 0, -1])
            cylinder(h = wall_thickness + 2, d = main_diameter - 4*wall_thickness);
        }
        
        // الغطاء العلوي
        translate([0, 0, main_length - wall_thickness]) {
            difference() {
                cylinder(h = wall_thickness, d = main_diameter);
                translate([0, 0, -1])
                cylinder(h = wall_thickness + 2, d = main_diameter - 4*wall_thickness);
            }
        }
    }
}

module drainage_slots() {
    if (include_drainage_holes) {
        for (i = [0:45:315]) {
            rotate([0, 0, i]) {
                translate([main_diameter/2 - wall_thickness/2, 0, main_length * 0.25]) {
                    cube([wall_thickness + 1, 8, 20], center = true);
                }
            }
        }
        
        for (i = [0:45:315]) {
            rotate([0, 0, i]) {
                translate([main_diameter/2 - wall_thickness/2, 0, main_length * 0.75]) {
                    cube([wall_thickness + 1, 8, 20], center = true);
                }
            }
        }
    }
}

module support_ribs() {
    if (include_support_ribs) {
        for (i = [0:90:270]) {
            rotate([0, 0, i]) {
                translate([0, 0, main_length/2]) {
                    cube([main_diameter - 2*wall_thickness - 10, 2, main_length - 100], center = true);
                }
            }
        }
    }
}

// === التجميع النهائي ===
// Final Assembly

module complete_pipe() {
    difference() {
        union() {
            main_pipe();
            branch_pipe();
            connection_joint();
            end_caps();
        }
        
        drainage_hole();
        drainage_slots();
    }
    
    support_ribs();
}

// عرض النموذج
complete_pipe();

// === معلومات التصميم ===
// Design Information

echo("=== أنبوب تصريف مستقيم ===");
echo(str("القطر الرئيسي: ", main_diameter, " مم"));
echo(str("الطول: ", main_length, " مم"));
echo(str("سماكة الجدار: ", wall_thickness, " مم"));
echo(str("قطر الفرع: ", branch_diameter, " مم"));
echo(str("زاوية الفرع: ", branch_angle, " درجة"));

// حساب الخصائص
main_volume = PI * pow(main_diameter/2, 2) * main_length / 1000000; // لتر
material_volume = PI * (pow(main_diameter/2, 2) - pow((main_diameter-2*wall_thickness)/2, 2)) * main_length / 1000000; // لتر
weight_pvc = material_volume * 1.4; // كيلوجرام (كثافة PVC)

echo(str("السعة الداخلية: ", main_volume, " لتر"));
echo(str("حجم المادة: ", material_volume, " لتر"));
echo(str("الوزن التقريبي (PVC): ", weight_pvc, " كجم"));
