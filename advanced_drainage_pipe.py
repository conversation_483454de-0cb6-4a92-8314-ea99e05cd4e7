#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تصميم متقدم لأنبوب تصريف المياه باستخدام OpenSCAD
Advanced Drainage Pipe Design using OpenSCAD
"""

import os
import subprocess
import math

class AdvancedDrainagePipe:
    def __init__(self):
        # المواصفات بالمليمتر للدقة
        self.main_diameter = 100      # 10 سم
        self.main_length = 1000       # 1 متر
        self.wall_thickness = 5       # 5 مم
        self.branch_angle = 45        # درجة
        self.branch_diameter = 80     # 8 سم
        self.branch_length = 150      # 15 سم
        self.branch_position = 700    # موضع الفرع من البداية
        
        # معاملات الطباعة ثلاثية الأبعاد
        self.print_tolerance = 0.2    # تسامح الطباعة
        self.support_needed = True    # هل نحتاج دعامات
        
    def generate_openscad_code(self):
        """إنشاء كود OpenSCAD للتصميم"""
        scad_code = f"""
// تصميم أنبوب تصريف المياه ثلاثي الأبعاد
// 3D Drainage Pipe Design
// المواصفات: قطر {self.main_diameter}مم، طول {self.main_length}مم، مخرج جانبي {self.branch_angle}°

// المتغيرات الأساسية
main_diameter = {self.main_diameter};
main_length = {self.main_length};
wall_thickness = {self.wall_thickness};
branch_angle = {self.branch_angle};
branch_diameter = {self.branch_diameter};
branch_length = {self.branch_length};
branch_position = {self.branch_position};

// دقة الدوران
$fn = 64;

module main_pipe() {{
    difference() {{
        // الأسطوانة الخارجية
        cylinder(h = main_length, d = main_diameter, center = false);
        
        // الأسطوانة الداخلية (التجويف)
        translate([0, 0, -1])
        cylinder(h = main_length + 2, d = main_diameter - 2*wall_thickness, center = false);
    }}
}}

module branch_pipe() {{
    translate([0, 0, branch_position]) {{
        rotate([0, branch_angle, 0]) {{
            translate([main_diameter/2 - wall_thickness, 0, 0]) {{
                difference() {{
                    // الأسطوانة الخارجية للفرع
                    cylinder(h = branch_length, d = branch_diameter, center = false);
                    
                    // الأسطوانة الداخلية للفرع
                    translate([0, 0, -1])
                    cylinder(h = branch_length + 2, d = branch_diameter - 2*wall_thickness, center = false);
                }}
            }}
        }}
    }}
}}

module connection_joint() {{
    // وصلة ربط الفرع بالأنبوب الرئيسي
    translate([0, 0, branch_position]) {{
        rotate([0, branch_angle, 0]) {{
            translate([main_diameter/2 - wall_thickness - 5, 0, -10]) {{
                difference() {{
                    cylinder(h = 20, d = branch_diameter + 10, center = false);
                    translate([0, 0, -1])
                    cylinder(h = 22, d = branch_diameter - 2*wall_thickness, center = false);
                }}
            }}
        }}
    }}
}}

module drainage_hole() {{
    // فتحة الاتصال بين الأنبوب الرئيسي والفرع
    translate([0, 0, branch_position]) {{
        rotate([0, branch_angle, 0]) {{
            translate([main_diameter/2 - wall_thickness - 1, 0, 0]) {{
                cylinder(h = 20, d = branch_diameter - 2*wall_thickness, center = true);
            }}
        }}
    }}
}}

module reinforcement_ring() {{
    // حلقة تقوية حول نقطة الاتصال
    translate([0, 0, branch_position - 15]) {{
        difference() {{
            cylinder(h = 30, d = main_diameter + 6, center = false);
            translate([0, 0, -1])
            cylinder(h = 32, d = main_diameter, center = false);
        }}
    }}
}}

module end_caps() {{
    // أغطية نهايات الأنبوب
    // الغطاء السفلي
    difference() {{
        cylinder(h = wall_thickness, d = main_diameter, center = false);
        translate([0, 0, -1])
        cylinder(h = wall_thickness + 2, d = main_diameter - 4*wall_thickness, center = false);
    }}
    
    // الغطاء العلوي
    translate([0, 0, main_length - wall_thickness]) {{
        difference() {{
            cylinder(h = wall_thickness, d = main_diameter, center = false);
            translate([0, 0, -1])
            cylinder(h = wall_thickness + 2, d = main_diameter - 4*wall_thickness, center = false);
        }}
    }}
}}

module drainage_slots() {{
    // فتحات تصريف إضافية
    for (i = [0:30:330]) {{
        rotate([0, 0, i]) {{
            translate([main_diameter/2 - wall_thickness/2, 0, 100]) {{
                cube([wall_thickness + 1, 8, 20], center = true);
            }}
        }}
    }}
    
    for (i = [0:30:330]) {{
        rotate([0, 0, i]) {{
            translate([main_diameter/2 - wall_thickness/2, 0, 200]) {{
                cube([wall_thickness + 1, 8, 20], center = true);
            }}
        }}
    }}
}}

module support_ribs() {{
    // أضلاع دعم داخلية
    for (i = [0:90:270]) {{
        rotate([0, 0, i]) {{
            translate([0, 0, main_length/2]) {{
                cube([main_diameter - 2*wall_thickness - 10, 2, main_length - 100], center = true);
            }}
        }}
    }}
}}

// التجميع النهائي
module complete_pipe() {{
    difference() {{
        union() {{
            main_pipe();
            branch_pipe();
            connection_joint();
            reinforcement_ring();
            end_caps();
        }}
        
        drainage_hole();
        drainage_slots();
    }}
    
    // إضافة الأضلاع الداخلية
    support_ribs();
}}

// عرض النموذج النهائي
complete_pipe();

// معلومات التصميم
echo("=== معلومات التصميم ===");
echo(str("القطر الرئيسي: ", main_diameter, " مم"));
echo(str("الطول الكلي: ", main_length, " مم"));
echo(str("سماكة الجدار: ", wall_thickness, " مم"));
echo(str("زاوية الفرع: ", branch_angle, " درجة"));
echo(str("قطر الفرع: ", branch_diameter, " مم"));

// حساب الحجم والوزن التقريبي
main_volume = PI * pow(main_diameter/2, 2) * main_length;
inner_volume = PI * pow((main_diameter - 2*wall_thickness)/2, 2) * main_length;
material_volume = main_volume - inner_volume;
pvc_density = 1.4; // جم/سم³
estimated_weight = material_volume * pvc_density / 1000; // بالجرام

echo(str("الحجم المادي التقريبي: ", material_volume/1000, " سم³"));
echo(str("الوزن التقريبي (PVC): ", estimated_weight, " جرام"));
"""
        return scad_code
    
    def save_scad_file(self, filename="drainage_pipe.scad"):
        """حفظ ملف OpenSCAD"""
        scad_code = self.generate_openscad_code()
        
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(scad_code)
        
        print(f"تم حفظ ملف OpenSCAD: {filename}")
        return filename
    
    def generate_stl(self, scad_file="drainage_pipe.scad", stl_file="drainage_pipe.stl"):
        """تحويل ملف OpenSCAD إلى STL"""
        try:
            # تشغيل OpenSCAD لتحويل الملف
            cmd = f'openscad -o "{stl_file}" "{scad_file}"'
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
            
            if result.returncode == 0:
                print(f"تم إنشاء ملف STL بنجاح: {stl_file}")
                return True
            else:
                print(f"خطأ في تحويل الملف: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"خطأ: {e}")
            print("تأكد من تثبيت OpenSCAD على النظام")
            return False
    
    def generate_print_instructions(self):
        """إنشاء تعليمات الطباعة ثلاثية الأبعاد"""
        instructions = f"""
=== تعليمات الطباعة ثلاثية الأبعاد ===

المواصفات:
- القطر الرئيسي: {self.main_diameter} مم
- الطول: {self.main_length} مم  
- سماكة الجدار: {self.wall_thickness} مم
- زاوية المخرج الجانبي: {self.branch_angle}°

إعدادات الطباعة المقترحة:
- سماكة الطبقة: 0.2-0.3 مم
- سرعة الطباعة: 50-60 مم/ثانية
- درجة حرارة الفوهة: 210-220°C (PLA) / 240-250°C (ABS)
- درجة حرارة السرير: 60°C (PLA) / 80-90°C (ABS)
- الملء: 20-30%
- الدعامات: مطلوبة للمخرج الجانبي
- التصاق السرير: استخدم Brim أو Raft

ملاحظات مهمة:
1. قم بطباعة الأنبوب في وضع عمودي للحصول على أفضل جودة
2. استخدم دعامات للمخرج الجانبي المائل
3. تأكد من معايرة الطابعة قبل البدء
4. قد تحتاج لتقسيم النموذج إلى أجزاء إذا كان كبيراً
5. استخدم مادة PLA للنماذج الأولية، ABS للاستخدام الفعلي

اختبار ما بعد الطباعة:
- تحقق من سلاسة التدفق الداخلي
- اختبر مقاومة التسرب
- تأكد من قوة الوصلات
"""
        return instructions

def main():
    """الدالة الرئيسية"""
    print("إنشاء تصميم متقدم لأنبوب تصريف المياه...")
    
    # إنشاء النموذج
    pipe = AdvancedDrainagePipe()
    
    # حفظ ملف OpenSCAD
    scad_file = pipe.save_scad_file("advanced_drainage_pipe.scad")
    
    # محاولة إنشاء ملف STL
    stl_success = pipe.generate_stl(scad_file, "advanced_drainage_pipe.stl")
    
    # طباعة تعليمات الطباعة
    print(pipe.generate_print_instructions())
    
    print("\nالملفات المُنشأة:")
    print(f"- ملف OpenSCAD: {scad_file}")
    if stl_success:
        print("- ملف STL: advanced_drainage_pipe.stl")
    else:
        print("- لإنشاء ملف STL، قم بفتح الملف .scad في OpenSCAD وتصديره")
    
    print("\nلعرض التصميم:")
    print("1. قم بتثبيت OpenSCAD من: https://openscad.org/")
    print("2. افتح الملف .scad في OpenSCAD")
    print("3. اضغط F5 للمعاينة أو F6 للرندر النهائي")

if __name__ == "__main__":
    main()
