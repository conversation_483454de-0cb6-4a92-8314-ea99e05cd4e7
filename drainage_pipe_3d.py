#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تصميم أنبوب تصريف مياه ثلاثي الأبعاد
3D Drainage Pipe Design
المواصفات:
- القطر: 10 سم
- الطول: 1 متر
- مخرج جانبي بزاوية 45 درجة
- مادة PVC
- قابل للطباعة ثلاثية الأبعاد
"""

import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
from mpl_toolkits.mplot3d.art3d import Poly3DCollection
import math

class DrainagePipe3D:
    def __init__(self):
        # المواصفات الأساسية
        self.main_diameter = 0.10  # 10 سم بالمتر
        self.main_length = 1.0     # 1 متر
        self.wall_thickness = 0.005  # 5 مم سماكة الجدار
        self.branch_angle = 45     # زاوية المخرج الجانبي
        self.branch_diameter = 0.08  # قطر المخرج الجانبي 8 سم
        self.branch_length = 0.15    # طول المخرج الجانبي 15 سم
        
        # نقاط التصميم
        self.vertices = []
        self.faces = []
        
    def create_cylinder(self, radius, height, center=(0, 0, 0), segments=32):
        """إنشاء أسطوانة"""
        vertices = []
        faces = []
        
        # النقاط العلوية والسفلية
        for i in range(segments):
            angle = 2 * math.pi * i / segments
            x = center[0] + radius * math.cos(angle)
            y = center[1] + radius * math.sin(angle)
            
            # النقطة السفلية
            vertices.append([x, y, center[2]])
            # النقطة العلوية
            vertices.append([x, y, center[2] + height])
        
        # إنشاء الوجوه الجانبية
        for i in range(segments):
            next_i = (i + 1) % segments
            
            # مثلث 1
            faces.append([2*i, 2*i+1, 2*next_i+1])
            # مثلث 2
            faces.append([2*i, 2*next_i+1, 2*next_i])
        
        # الوجه السفلي
        bottom_face = [2*i for i in range(segments)]
        faces.append(bottom_face)
        
        # الوجه العلوي
        top_face = [2*i+1 for i in range(segments)]
        faces.append(top_face)
        
        return vertices, faces
    
    def create_hollow_cylinder(self, outer_radius, inner_radius, height, center=(0, 0, 0)):
        """إنشاء أسطوانة مجوفة"""
        outer_verts, outer_faces = self.create_cylinder(outer_radius, height, center)
        inner_verts, inner_faces = self.create_cylinder(inner_radius, height, center)
        
        # دمج النقاط
        all_vertices = outer_verts + inner_verts
        
        # تعديل فهارس الوجوه الداخلية
        adjusted_inner_faces = []
        for face in inner_faces:
            adjusted_face = [idx + len(outer_verts) for idx in face]
            adjusted_inner_faces.append(adjusted_face)
        
        # دمج الوجوه (عكس اتجاه الوجوه الداخلية)
        all_faces = outer_faces + [face[::-1] for face in adjusted_inner_faces]
        
        return all_vertices, all_faces
    
    def create_angled_branch(self, position, angle_deg):
        """إنشاء المخرج الجانبي المائل"""
        angle_rad = math.radians(angle_deg)
        
        # حساب الاتجاه
        direction = np.array([math.cos(angle_rad), 0, math.sin(angle_rad)])
        
        # إنشاء الأسطوانة المائلة
        branch_vertices = []
        segments = 16
        
        for i in range(segments + 1):
            t = i / segments * self.branch_length
            center = np.array(position) + direction * t
            
            for j in range(16):
                angle = 2 * math.pi * j / 16
                radius = self.branch_diameter / 2
                
                # نقاط على محيط الدائرة
                local_x = radius * math.cos(angle)
                local_y = radius * math.sin(angle)
                
                # تدوير النقاط حسب زاوية الفرع
                rotated_x = local_x
                rotated_y = local_y * math.cos(angle_rad) - 0 * math.sin(angle_rad)
                rotated_z = local_y * math.sin(angle_rad) + 0 * math.cos(angle_rad)
                
                point = center + np.array([rotated_x, rotated_y, rotated_z])
                branch_vertices.append(point.tolist())
        
        # إنشاء الوجوه للفرع
        branch_faces = []
        for i in range(segments):
            for j in range(16):
                next_j = (j + 1) % 16
                
                # مربع من أربع نقاط
                p1 = i * 16 + j
                p2 = i * 16 + next_j
                p3 = (i + 1) * 16 + next_j
                p4 = (i + 1) * 16 + j
                
                # تقسيم المربع إلى مثلثين
                branch_faces.append([p1, p2, p3])
                branch_faces.append([p1, p3, p4])
        
        return branch_vertices, branch_faces
    
    def generate_model(self):
        """إنشاء النموذج الكامل"""
        # الأنبوب الرئيسي
        main_outer_radius = self.main_diameter / 2
        main_inner_radius = main_outer_radius - self.wall_thickness
        
        main_vertices, main_faces = self.create_hollow_cylinder(
            main_outer_radius, main_inner_radius, self.main_length
        )
        
        # المخرج الجانبي
        branch_position = [0, 0, self.main_length * 0.7]  # في 70% من طول الأنبوب
        branch_vertices, branch_faces = self.create_angled_branch(
            branch_position, self.branch_angle
        )
        
        # دمج جميع النقاط والوجوه
        all_vertices = main_vertices + branch_vertices
        
        # تعديل فهارس وجوه الفرع
        adjusted_branch_faces = []
        for face in branch_faces:
            adjusted_face = [idx + len(main_vertices) for idx in face]
            adjusted_branch_faces.append(adjusted_face)
        
        all_faces = main_faces + adjusted_branch_faces
        
        self.vertices = all_vertices
        self.faces = all_faces
        
        return all_vertices, all_faces
    
    def export_to_stl(self, filename="drainage_pipe.stl"):
        """تصدير النموذج إلى ملف STL للطباعة ثلاثية الأبعاد"""
        if not self.vertices or not self.faces:
            self.generate_model()
        
        with open(filename, 'w') as f:
            f.write("solid drainage_pipe\n")
            
            for face in self.faces:
                if len(face) >= 3:
                    # حساب المتجه العمودي
                    v1 = np.array(self.vertices[face[1]]) - np.array(self.vertices[face[0]])
                    v2 = np.array(self.vertices[face[2]]) - np.array(self.vertices[face[0]])
                    normal = np.cross(v1, v2)
                    normal = normal / np.linalg.norm(normal)
                    
                    f.write(f"  facet normal {normal[0]:.6f} {normal[1]:.6f} {normal[2]:.6f}\n")
                    f.write("    outer loop\n")
                    
                    for vertex_idx in face[:3]:  # STL يدعم المثلثات فقط
                        vertex = self.vertices[vertex_idx]
                        f.write(f"      vertex {vertex[0]:.6f} {vertex[1]:.6f} {vertex[2]:.6f}\n")
                    
                    f.write("    endloop\n")
                    f.write("  endfacet\n")
            
            f.write("endsolid drainage_pipe\n")
        
        print(f"تم تصدير النموذج إلى: {filename}")
    
    def visualize(self):
        """عرض النموذج ثلاثي الأبعاد"""
        if not self.vertices or not self.faces:
            self.generate_model()
        
        fig = plt.figure(figsize=(12, 8))
        ax = fig.add_subplot(111, projection='3d')
        
        # تحويل الوجوه إلى مضلعات
        polygons = []
        for face in self.faces:
            if len(face) >= 3:
                polygon = [self.vertices[i] for i in face]
                polygons.append(polygon)
        
        # إضافة المضلعات
        poly_collection = Poly3DCollection(polygons, alpha=0.7, facecolor='lightblue', edgecolor='black')
        ax.add_collection3d(poly_collection)
        
        # تحديد حدود المحاور
        vertices_array = np.array(self.vertices)
        ax.set_xlim([vertices_array[:, 0].min(), vertices_array[:, 0].max()])
        ax.set_ylim([vertices_array[:, 1].min(), vertices_array[:, 1].max()])
        ax.set_zlim([vertices_array[:, 2].min(), vertices_array[:, 2].max()])
        
        ax.set_xlabel('X (متر)')
        ax.set_ylabel('Y (متر)')
        ax.set_zlabel('Z (متر)')
        ax.set_title('تصميم أنبوب تصريف المياه ثلاثي الأبعاد\nDrainage Pipe 3D Design')
        
        plt.tight_layout()
        plt.show()

def main():
    """الدالة الرئيسية"""
    print("إنشاء تصميم أنبوب تصريف المياه ثلاثي الأبعاد...")
    print("Creating 3D drainage pipe design...")
    
    # إنشاء النموذج
    pipe = DrainagePipe3D()
    
    # إنشاء التصميم
    vertices, faces = pipe.generate_model()
    
    print(f"تم إنشاء النموذج بنجاح!")
    print(f"عدد النقاط: {len(vertices)}")
    print(f"عدد الوجوه: {len(faces)}")
    
    # تصدير إلى STL
    pipe.export_to_stl("drainage_pipe_10cm_1m.stl")
    
    # عرض النموذج
    pipe.visualize()
    
    # طباعة المواصفات
    print("\nمواصفات التصميم:")
    print(f"- القطر الرئيسي: {pipe.main_diameter * 100} سم")
    print(f"- الطول: {pipe.main_length} متر")
    print(f"- سماكة الجدار: {pipe.wall_thickness * 1000} مم")
    print(f"- زاوية المخرج الجانبي: {pipe.branch_angle} درجة")
    print(f"- قطر المخرج الجانبي: {pipe.branch_diameter * 100} سم")

if __name__ == "__main__":
    main()
