// أنبوب تصريف مياه رئيسي مع مخرج جانبي بزاوية 45 درجة
// القطر الداخلي: 100 مم، الطول: 1000 مم، سمك الجدار: 4 مم
// مخرج جانبي: قطر داخلي 50 مم، طول 100 مم، زاوية 45 درجة

$fn=120; // دقة التدوير

// المتغيرات
main_inner_d = 100;    // القطر الداخلي للأنبوب الرئيسي (مم)
main_length  = 1000;   // طول الأنبوب الرئيسي (مم)
wall_thick   = 4;      // سمك الجدار (مم)

side_inner_d = 50;     // القطر الداخلي للمخرج الجانبي (مم)
side_length  = 100;    // طول المخرج الجانبي (مم)
side_angle   = 45;     // زاوية المخرج الجانبي (درجة)

// أنبوب رئيسي
module main_pipe() {
    difference() {
        cylinder(d=main_inner_d+2*wall_thick, h=main_length, center=false);
        translate([0,0,-1])
            cylinder(d=main_inner_d, h=main_length+2, center=false);
    }
}

// مخرج جانبي
module side_outlet() {
    difference() {
        cylinder(d=side_inner_d+2*wall_thick, h=side_length, center=false);
        translate([0,0,-1])
            cylinder(d=side_inner_d, h=side_length+2, center=false);
    }
}

// تجميع الأنبوب مع المخرج الجانبي
module pipe_with_side_outlet() {
    union() {
        main_pipe();
        // وضع المخرج الجانبي على جانب الأنبوب الرئيسي بزاوية 45 درجة
        translate([
            (main_inner_d/2+wall_thick)-(side_inner_d/2+wall_thick),
            0,
            main_length/2
        ])
        rotate([0,side_angle,0])
            side_outlet();
    }
}

// استدعاء النموذج النهائي
pipe_with_side_outlet(); 