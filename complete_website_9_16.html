<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>أجمل الدول العربية من حيث الطبيعة</title>
    <link href="https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <style>
        /* Reset and Base Styles */
        * {
            margin: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            line-height: 1.6;
            color: #333;
            direction: rtl;
            text-align: right;
            width: 100vw;
            height: auto;
            min-height: 177.78vw; /* 9:16 aspect ratio */
            max-width: 1080px;
            margin: 0 auto;
            overflow-x: hidden;
            background: #000;
        }

        .container {
            max-width: 100%;
            margin: 0 auto;
            padding: 0 15px;
        }

        /* Hero Section */
        .hero {
            height: 35vh;
            background: linear-gradient(rgba(0,0,0,0.4), rgba(0,0,0,0.4)),
                        url('https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=1080&h=1920&fit=crop') center/cover;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            color: white;
            text-align: center;
            background-size: cover;
            background-position: center;
        }

        .hero-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(45deg, rgba(0,100,0,0.3), rgba(0,50,100,0.3));
        }

        .hero-content {
            position: relative;
            z-index: 2;
            max-width: 90%;
            padding: 0 15px;
        }

        .hero-title {
            font-family: 'Amiri', serif;
            font-size: 2.2rem;
            font-weight: 700;
            margin-bottom: 0.8rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
            animation: fadeInUp 1s ease-out;
            line-height: 1.2;
        }

        .hero-subtitle {
            font-size: 1rem;
            margin-bottom: 1.5rem;
            opacity: 0.9;
            animation: fadeInUp 1s ease-out 0.3s both;
            line-height: 1.4;
        }

        .cta-button {
            background: linear-gradient(45deg, #2ecc71, #27ae60);
            color: white;
            border: none;
            padding: 12px 30px;
            font-size: 1rem;
            font-family: 'Cairo', sans-serif;
            border-radius: 50px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(46, 204, 113, 0.3);
            animation: fadeInUp 1s ease-out 0.6s both;
        }

        .cta-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 20px rgba(46, 204, 113, 0.4);
        }

        .cta-button i {
            margin-left: 10px;
        }

        .scroll-indicator {
            position: absolute;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            animation: bounce 2s infinite;
        }

        .scroll-indicator i {
            font-size: 1.5rem;
            opacity: 0.7;
        }

        /* Countries Section */
        .countries {
            padding: 30px 0;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            min-height: 55vh;
        }

        .section-title {
            font-family: 'Amiri', serif;
            font-size: 1.8rem;
            text-align: center;
            margin-bottom: 1.5rem;
            color: #2c3e50;
            position: relative;
            line-height: 1.3;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -8px;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 3px;
            background: linear-gradient(45deg, #2ecc71, #27ae60);
            border-radius: 2px;
        }

        /* Table Styles */
        .table-container {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin: 1rem 5px;
            overflow-x: auto;
            max-height: 60vh;
            overflow-y: auto;
        }

        .countries-table {
            width: 100%;
            border-collapse: collapse;
            font-family: 'Cairo', sans-serif;
        }

        .countries-table thead {
            background: linear-gradient(45deg, #2c3e50, #34495e);
            color: white;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .countries-table th {
            padding: 12px 6px;
            text-align: center;
            font-family: 'Amiri', serif;
            font-size: 0.85rem;
            font-weight: 700;
            border-bottom: 3px solid #2ecc71;
            line-height: 1.2;
        }

        .countries-table tbody tr {
            transition: all 0.3s ease;
            border-bottom: 1px solid #e9ecef;
        }

        .countries-table tbody tr:hover {
            background: linear-gradient(45deg, rgba(46, 204, 113, 0.05), rgba(39, 174, 96, 0.05));
            transform: scale(1.01);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .countries-table tbody tr:nth-child(even) {
            background: rgba(248, 249, 250, 0.5);
        }

        .countries-table td {
            padding: 10px 6px;
            vertical-align: top;
            border-right: 1px solid #e9ecef;
            font-size: 0.8rem;
            line-height: 1.4;
        }

        .countries-table td:last-child {
            border-right: none;
        }

        .country-name h3 {
            font-family: 'Amiri', serif;
            font-size: 1rem;
            color: #2c3e50;
            margin-bottom: 3px;
            font-weight: 700;
            line-height: 1.2;
        }

        .country-subtitle {
            color: #27ae60;
            font-weight: 600;
            font-size: 0.7rem;
            display: block;
            line-height: 1.2;
        }

        .country-description {
            color: #666;
            line-height: 1.5;
            font-size: 0.75rem;
            max-width: 200px;
        }

        .country-highlights {
            display: flex;
            flex-wrap: wrap;
            gap: 3px;
        }

        .highlight-tag {
            display: inline-block;
            background: linear-gradient(45deg, #3498db, #2980b9);
            color: white;
            padding: 2px 6px;
            border-radius: 10px;
            font-size: 0.6rem;
            margin: 1px;
            white-space: nowrap;
            line-height: 1.2;
        }

        .best-time {
            color: #27ae60;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 4px;
            font-size: 0.7rem;
            line-height: 1.3;
        }

        .best-time i {
            color: #2ecc71;
            font-size: 0.8rem;
        }

        /* Highlights Section */
        .highlights {
            padding: 25px 0;
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            min-height: 25vh;
        }

        .highlights .section-title {
            color: white;
        }

        .highlights-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
            margin-top: 1.5rem;
            padding: 0 15px;
        }

        .highlight-item {
            text-align: center;
            padding: 15px 10px;
            background: rgba(255,255,255,0.1);
            border-radius: 12px;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }

        .highlight-item:hover {
            transform: translateY(-3px);
            background: rgba(255,255,255,0.15);
        }

        .highlight-icon {
            width: 40px;
            height: 40px;
            background: linear-gradient(45deg, #2ecc71, #27ae60);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 10px;
            font-size: 1.1rem;
        }

        .highlight-item h3 {
            font-family: 'Amiri', serif;
            font-size: 0.9rem;
            margin-bottom: 8px;
            line-height: 1.3;
        }

        .highlight-item p {
            opacity: 0.9;
            line-height: 1.4;
            font-size: 0.75rem;
        }

        /* Footer */
        .footer {
            background: #1a1a1a;
            color: white;
            padding: 20px 0 15px;
        }

        .footer-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 15px;
        }

        .footer-section h3,
        .footer-section h4 {
            font-family: 'Amiri', serif;
            margin-bottom: 10px;
            color: #2ecc71;
            font-size: 1rem;
        }

        .footer-section p {
            font-size: 0.8rem;
            line-height: 1.4;
        }

        .social-links {
            display: flex;
            gap: 10px;
        }

        .social-links a {
            width: 35px;
            height: 35px;
            background: linear-gradient(45deg, #2ecc71, #27ae60);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            text-decoration: none;
            transition: all 0.3s ease;
            font-size: 0.9rem;
        }

        .social-links a:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(46, 204, 113, 0.3);
        }

        .footer-bottom {
            text-align: center;
            padding-top: 15px;
            border-top: 1px solid #333;
            opacity: 0.7;
            font-size: 0.8rem;
        }

        /* Animations */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateX(-50%) translateY(0);
            }
            40% {
                transform: translateX(-50%) translateY(-10px);
            }
            60% {
                transform: translateX(-50%) translateY(-5px);
            }
        }

        /* Mobile Responsive */
        @media (max-width: 480px) {
            .hero-title {
                font-size: 1.8rem;
            }

            .hero-subtitle {
                font-size: 0.9rem;
            }

            .section-title {
                font-size: 1.5rem;
            }

            .countries-table th {
                font-size: 0.75rem;
                padding: 8px 4px;
            }

            .countries-table td {
                font-size: 0.7rem;
                padding: 8px 4px;
            }

            .country-name h3 {
                font-size: 0.9rem;
            }

            .country-description {
                font-size: 0.65rem;
                max-width: 150px;
            }

            .highlight-tag {
                font-size: 0.55rem;
                padding: 1px 4px;
            }

            .best-time {
                font-size: 0.65rem;
            }

            .highlight-item h3 {
                font-size: 0.8rem;
            }

            .highlight-item p {
                font-size: 0.65rem;
            }
        }
    </style>
</head>
<body>
    <!-- Hero Section -->
    <section class="hero" id="hero">
        <div class="hero-overlay"></div>
        <div class="hero-content">
            <h1 class="hero-title">اكتشف روعة الطبيعة في العالم العربي</h1>
            <p class="hero-subtitle">رحلة عبر أجمل المناظر الطبيعية في الوطن العربي</p>
            <button class="cta-button" onclick="scrollToCountries()">
                <i class="fas fa-compass"></i>
                ابدأ رحلتك
            </button>
        </div>
        <div class="scroll-indicator">
            <i class="fas fa-chevron-down"></i>
        </div>
    </section>

    <!-- Countries Section -->
    <section class="countries" id="countries">
        <div class="container">
            <h2 class="section-title">أجمل الدول العربية طبيعياً</h2>
            <div class="table-container">
                <table class="countries-table">
                    <thead>
                        <tr>
                            <th>الدولة</th>
                            <th>الوصف</th>
                            <th>المعالم الطبيعية الرئيسية</th>
                            <th>أفضل وقت للزيارة</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr class="country-row">
                            <td class="country-name" data-label="الدولة">
                                <h3>لبنان</h3>
                                <span class="country-subtitle">سويسرا الشرق</span>
                            </td>
                            <td class="country-description" data-label="الوصف">
                                جبال الأرز الشامخة، وديان خضراء، وشواطئ البحر المتوسط الساحرة
                            </td>
                            <td class="country-highlights" data-label="المعالم الطبيعية">
                                <span class="highlight-tag">جبال الأرز</span>
                                <span class="highlight-tag">وادي قاديشا</span>
                                <span class="highlight-tag">بحيرة القرعون</span>
                            </td>
                            <td class="best-time" data-label="أفضل وقت للزيارة">
                                <i class="fas fa-calendar"></i>
                                الربيع والخريف
                            </td>
                        </tr>
                        <tr class="country-row">
                            <td class="country-name" data-label="الدولة">
                                <h3>المغرب</h3>
                                <span class="country-subtitle">تنوع طبيعي مذهل</span>
                            </td>
                            <td class="country-description" data-label="الوصف">
                                من جبال الأطلس الشامخة إلى الصحراء الذهبية وشواطئ المحيط الأطلسي
                            </td>
                            <td class="country-highlights" data-label="المعالم الطبيعية">
                                <span class="highlight-tag">جبال الأطلس</span>
                                <span class="highlight-tag">صحراء مرزوقة</span>
                                <span class="highlight-tag">شلالات أوزود</span>
                            </td>
                            <td class="best-time" data-label="أفضل وقت للزيارة">
                                <i class="fas fa-calendar"></i>
                                الربيع والخريف
                            </td>
                        </tr>
                        <tr class="country-row">
                            <td class="country-name" data-label="الدولة">
                                <h3>الجزائر</h3>
                                <span class="country-subtitle">أكبر دولة أفريقية</span>
                            </td>
                            <td class="country-description" data-label="الوصف">
                                تضاريس متنوعة من الساحل المتوسطي إلى الصحراء الكبرى والهضاب العليا
                            </td>
                            <td class="country-highlights" data-label="المعالم الطبيعية">
                                <span class="highlight-tag">جبال الهقار</span>
                                <span class="highlight-tag">واحة تيميمون</span>
                                <span class="highlight-tag">ساحل تيبازة</span>
                            </td>
                            <td class="best-time" data-label="أفضل وقت للزيارة">
                                <i class="fas fa-calendar"></i>
                                الربيع والخريف
                            </td>
                        </tr>
                        <tr class="country-row">
                            <td class="country-name" data-label="الدولة">
                                <h3>سلطنة عمان</h3>
                                <span class="country-subtitle">جوهرة الخليج</span>
                            </td>
                            <td class="country-description" data-label="الوصف">
                                جبال خضراء، شواطئ بكر، وديان عميقة، وصحارى ذهبية ساحرة
                            </td>
                            <td class="country-highlights" data-label="المعالم الطبيعية">
                                <span class="highlight-tag">جبل شمس</span>
                                <span class="highlight-tag">وادي شاب</span>
                                <span class="highlight-tag">صحراء وهيبة</span>
                            </td>
                            <td class="best-time" data-label="أفضل وقت للزيارة">
                                <i class="fas fa-calendar"></i>
                                الشتاء والربيع
                            </td>
                        </tr>
                        <tr class="country-row">
                            <td class="country-name" data-label="الدولة">
                                <h3>العراق</h3>
                                <span class="country-subtitle">بلاد الرافدين</span>
                            </td>
                            <td class="country-description" data-label="الوصف">
                                أرض النهرين دجلة والفرات، جبال كردستان الخضراء، وأهوار الجنوب الساحرة
                            </td>
                            <td class="country-highlights" data-label="المعالم الطبيعية">
                                <span class="highlight-tag">جبال كردستان</span>
                                <span class="highlight-tag">أهوار الجنوب</span>
                                <span class="highlight-tag">شلال كلي علي بيك</span>
                            </td>
                            <td class="best-time" data-label="أفضل وقت للزيارة">
                                <i class="fas fa-calendar"></i>
                                الربيع والخريف
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </section>

    <!-- Highlights Section -->
    <section class="highlights" id="highlights">
        <div class="container">
            <h2 class="section-title">التنوع الطبيعي في العالم العربي</h2>
            <div class="highlights-grid">
                <div class="highlight-item">
                    <div class="highlight-icon">
                        <i class="fas fa-mountain"></i>
                    </div>
                    <h3>الجبال الشامخة</h3>
                    <p>من جبال الأطلس في المغرب إلى جبال لبنان وكردستان العراق</p>
                </div>
                <div class="highlight-item">
                    <div class="highlight-icon">
                        <i class="fas fa-water"></i>
                    </div>
                    <h3>الشواطئ الساحرة</h3>
                    <p>سواحل البحر المتوسط والمحيط الأطلسي والخليج العربي</p>
                </div>
                <div class="highlight-item">
                    <div class="highlight-icon">
                        <i class="fas fa-seedling"></i>
                    </div>
                    <h3>الواحات الخضراء</h3>
                    <p>واحات النخيل والينابيع الطبيعية في قلب الصحراء</p>
                </div>
                <div class="highlight-item">
                    <div class="highlight-icon">
                        <i class="fas fa-sun"></i>
                    </div>
                    <h3>الصحارى الذهبية</h3>
                    <p>من الصحراء الكبرى إلى الربع الخالي، مناظر خلابة لا تُنسى</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>اكتشف العالم العربي</h3>
                    <p>رحلة عبر أجمل المناظر الطبيعية في الوطن العربي</p>
                </div>
                <div class="footer-section">
                    <h4>تواصل معنا</h4>
                    <div class="social-links">
                        <a href="#"><i class="fab fa-facebook"></i></a>
                        <a href="#"><i class="fab fa-twitter"></i></a>
                        <a href="#"><i class="fab fa-instagram"></i></a>
                        <a href="#"><i class="fab fa-youtube"></i></a>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 أجمل الدول العربية. جميع الحقوق محفوظة.</p>
            </div>
        </div>
    </footer>

    <script>
        // Smooth scrolling function
        function scrollToCountries() {
            document.getElementById('countries').scrollIntoView({
                behavior: 'smooth'
            });
        }

        // Parallax effect for hero section
        function parallaxEffect() {
            const hero = document.querySelector('.hero');
            const scrolled = window.pageYOffset;
            const rate = scrolled * -0.3;

            if (hero) {
                hero.style.transform = `translateY(${rate}px)`;
            }
        }

        // Add hover effects to table rows
        function addTableEffects() {
            const rows = document.querySelectorAll('.country-row');

            rows.forEach(row => {
                row.addEventListener('mouseenter', function() {
                    this.style.transform = 'scale(1.02)';
                    this.style.boxShadow = '0 8px 25px rgba(0,0,0,0.15)';
                });

                row.addEventListener('mouseleave', function() {
                    this.style.transform = 'scale(1)';
                    this.style.boxShadow = 'none';
                });
            });
        }

        // Intersection Observer for animations
        function setupIntersectionObserver() {
            const observerOptions = {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            };

            const observer = new IntersectionObserver(function(entries) {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('animate-in');
                    }
                });
            }, observerOptions);

            // Observe elements
            const elementsToObserve = document.querySelectorAll('.country-row, .highlight-item');
            elementsToObserve.forEach(element => {
                observer.observe(element);
            });
        }

        // Add CSS for intersection observer animations
        function addAnimationStyles() {
            const style = document.createElement('style');
            style.textContent = `
                .country-row, .highlight-item {
                    opacity: 0;
                    transform: translateY(20px);
                    transition: all 0.6s ease;
                }

                .country-row.animate-in, .highlight-item.animate-in {
                    opacity: 1;
                    transform: translateY(0);
                }

                .country-row:nth-child(1) { transition-delay: 0.1s; }
                .country-row:nth-child(2) { transition-delay: 0.2s; }
                .country-row:nth-child(3) { transition-delay: 0.3s; }
                .country-row:nth-child(4) { transition-delay: 0.4s; }
                .country-row:nth-child(5) { transition-delay: 0.5s; }

                .highlight-item:nth-child(1) { transition-delay: 0.1s; }
                .highlight-item:nth-child(2) { transition-delay: 0.2s; }
                .highlight-item:nth-child(3) { transition-delay: 0.3s; }
                .highlight-item:nth-child(4) { transition-delay: 0.4s; }
            `;
            document.head.appendChild(style);
        }

        // Initialize everything when DOM is loaded
        document.addEventListener('DOMContentLoaded', function() {
            addAnimationStyles();
            setupIntersectionObserver();
            addTableEffects();

            // Add scroll event listeners
            window.addEventListener('scroll', function() {
                parallaxEffect();
            });

            // Add click event to scroll indicator
            const scrollIndicator = document.querySelector('.scroll-indicator');
            if (scrollIndicator) {
                scrollIndicator.addEventListener('click', scrollToCountries);
            }
        });

        // Performance optimization: throttle scroll events
        function throttle(func, limit) {
            let inThrottle;
            return function() {
                const args = arguments;
                const context = this;
                if (!inThrottle) {
                    func.apply(context, args);
                    inThrottle = true;
                    setTimeout(() => inThrottle = false, limit);
                }
            }
        }

        // Apply throttling to scroll events
        window.addEventListener('scroll', throttle(function() {
            parallaxEffect();
        }, 16)); // ~60fps
    </script>
</body>
</html>
